<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Skin Analysis Test</title>
    <style>
      body {
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
        background-color: #f5f5f5;
      }

      .main-container {
        display: flex;
        gap: 30px;
        margin-top: 20px;
      }

      .left-panel {
        flex: 1;
        background: white;
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        height: fit-content;
      }

      .right-panel {
        flex: 1;
        background: white;
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      }

      .upload-area {
        border: 2px dashed #ccc;
        border-radius: 15px;
        padding: 40px;
        text-align: center;
        margin: 20px 0;
        background: #fafafa;
        transition: all 0.3s ease;
      }
      .upload-area:hover {
        border-color: #007bff;
        background: #f0f8ff;
      }

      .image-preview {
        margin-top: 20px;
        text-align: center;
      }

      .preview-image {
        max-width: 100%;
        max-height: 400px;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
      }

      .capture-circle {
        width: 300px;
        height: 300px;
        border: 3px solid white;
        border-radius: 50%;
        position: relative;
        margin: 20px auto;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 18px;
        text-align: center;
        line-height: 1.4;
      }

      .instruction-bars {
        position: absolute;
        top: -40px;
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        gap: 5px;
      }

      .instruction-bar {
        padding: 8px 15px;
        font-size: 12px;
        font-weight: bold;
        color: white;
        border-radius: 5px;
      }

      .lighting { background-color: #dc3545; }
      .look-straight { background-color: #dc3545; }
      .face-position { background-color: #dc3545; }

      .result {
        margin-top: 20px;
        padding: 20px;
        background-color: #f8f9fa;
        border-radius: 5px;
        white-space: pre-wrap;
      }

      .skin-analysis-results {
        background: white;
        border-radius: 15px;
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
      }

      .skin-age-header {
        text-align: center;
        font-size: 28px;
        font-weight: bold;
        color: #333;
        margin-bottom: 30px;
        padding-bottom: 15px;
        border-bottom: 2px solid #eee;
        letter-spacing: 2px;
      }

      .scores-grid {
        display: grid;
        grid-template-columns: repeat(5, 1fr);
        gap: 15px;
        margin-bottom: 20px;
      }

      .score-item {
        text-align: center;
        padding: 10px;
      }

      .score-circle {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 18px;
        font-weight: bold;
        color: #fff;
        text-align: center;
        margin: 0 auto 10px;
        position: relative;
        border: 3px solid rgba(255, 255, 255, 0.3);
      }

      .score-value {
        font-size: 16px;
        font-weight: bold;
      }

      .score-label {
        font-size: 12px;
        color: #666;
        font-weight: 500;
        margin-top: 8px;
      }
      /* Skin analysis specific colors */
      .skin-type { background: linear-gradient(135deg, #8B7355, #A0956B); }
      .spots { background: linear-gradient(135deg, #00BFFF, #1E90FF); }
      .wrinkles { background: linear-gradient(135deg, #90EE90, #32CD32); }
      .texture { background: linear-gradient(135deg, #DDA0DD, #BA55D3); }
      .acne { background: linear-gradient(135deg, #87CEEB, #4682B4); }
      .dark-circles { background: linear-gradient(135deg, #D3D3D3, #A9A9A9); }
      .redness { background: linear-gradient(135deg, #FF6347, #DC143C); }
      .oiliness { background: linear-gradient(135deg, #FFA500, #FF8C00); }
      .moisture { background: linear-gradient(135deg, #40E0D0, #00CED1); }
      .pores { background: linear-gradient(135deg, #98FB98, #90EE90); }
      .eye-bags { background: linear-gradient(135deg, #FFB6C1, #FF69B4); }
      .radiance { background: linear-gradient(135deg, #E6E6FA, #DDA0DD); }
      .firmness { background: linear-gradient(135deg, #F0E68C, #DAA520); }
      .droopy-upper { background: linear-gradient(135deg, #DDA0DD, #9370DB); }
      .droopy-lower { background: linear-gradient(135deg, #FF1493, #DC143C); }

      .excellent {
        background: linear-gradient(135deg, #28a745, #20c997);
      }
      .good {
        background: linear-gradient(135deg, #17a2b8, #20c997);
      }
      .fair {
        background: linear-gradient(135deg, #ffc107, #fd7e14);
        color: #333 !important;
      }
      .poor {
        background: linear-gradient(135deg, #fd7e14, #dc3545);
      }
      .very-poor {
        background: linear-gradient(135deg, #dc3545, #c82333);
      }
      .details-section {
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid #eee;
      }
      .detail-item {
        display: flex;
        justify-content: space-between;
        padding: 8px 0;
        border-bottom: 1px solid #f0f0f0;
      }
      .detail-label {
        font-weight: 500;
        color: #555;
      }
      .detail-value {
        color: #333;
      }
      .loading {
        display: none;
        text-align: center;
        margin: 20px 0;
      }
      button {
        background-color: #007bff;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
      }
      button:hover {
        background-color: #0056b3;
      }
      button:disabled {
        background-color: #6c757d;
        cursor: not-allowed;
      }
    </style>
  </head>
  <body>
    <h1 style="text-align: center; color: #333; margin-bottom: 30px;">Face Analyzer</h1>

    <div class="main-container">
      <!-- Left Panel - Image Upload and Preview -->
      <div class="left-panel">
        <h2 style="text-align: center; color: #333; margin-bottom: 20px;">Upload Image</h2>

        <div class="upload-area">
          <input
            type="file"
            id="imageInput"
            accept="image/*"
            style="display: none"
          />
          <div class="capture-circle" id="captureCircle">
            <div class="instruction-bars">
              <div class="instruction-bar lighting">Lighting<br>Not Good</div>
              <div class="instruction-bar look-straight">Look Straight<br>Not Good</div>
              <div class="instruction-bar face-position">Face Position<br>Not Good</div>
            </div>
            Keep your face<br>inside the circle
          </div>
          <p style="margin-top: 20px;">Click to select an image or drag and drop</p>
          <button onclick="document.getElementById('imageInput').click()" style="margin-top: 10px;">
            Select Image
          </button>
        </div>

        <div class="image-preview" id="imagePreview" style="display: none;">
          <img id="previewImg" class="preview-image" alt="Preview" />
        </div>

        <div style="text-align: center; margin-top: 20px;">
          <button id="analyzeBtn" onclick="analyzeSkin()" disabled style="padding: 15px 30px; font-size: 16px;">
            Analyze Skin
          </button>
          <button id="testBtn" onclick="showTestData()" style="padding: 15px 30px; font-size: 16px; margin-left: 10px; background-color: #28a745;">
            Show Test Data
          </button>
        </div>

        <div class="loading" id="loading">
          <p>Analyzing image... Please wait.</p>
        </div>
      </div>

      <!-- Right Panel - Results -->
      <div class="right-panel">
        <div class="result" id="result" style="display: none;"></div>
        <div id="welcomeMessage" style="text-align: center; color: #666; margin-top: 100px;">
          <h3>Welcome to Face Analyzer</h3>
          <p>Upload an image or click "Show Test Data" to see sample results</p>
        </div>
      </div>
    </div>

    <script>
      const imageInput = document.getElementById("imageInput");
      const analyzeBtn = document.getElementById("analyzeBtn");
      const loading = document.getElementById("loading");
      const result = document.getElementById("result");
      const imagePreview = document.getElementById("imagePreview");
      const previewImg = document.getElementById("previewImg");
      const captureCircle = document.getElementById("captureCircle");
      const welcomeMessage = document.getElementById("welcomeMessage");

      imageInput.addEventListener("change", function (e) {
        if (e.target.files.length > 0) {
          const file = e.target.files[0];
          analyzeBtn.disabled = false;
          analyzeBtn.textContent = `Analyze: ${file.name}`;

          // Show image preview
          const reader = new FileReader();
          reader.onload = function(e) {
            previewImg.src = e.target.result;
            imagePreview.style.display = "block";
            captureCircle.style.display = "none";
          };
          reader.readAsDataURL(file);
        }
      });

      // Test data function
      function showTestData() {
        welcomeMessage.style.display = "none";
        const testData = {
          result: {
            skin_age: { value: 28 },
            score_info: {
              melanin_score: 85,
              wrinkle_score: 92,
              rough_score: 78,
              acne_score: 88,
              dark_circle_score: 75,
              sensitivity_score: 82,
              oily_intensity_score: 70,
              water_score: 85,
              pores_score: 80,
              total_score: 83
            }
          }
        };
        displaySkinAnalysisResults(testData);
      }

      async function analyzeSkin() {
        const file = imageInput.files[0];
        if (!file) {
          alert("Please select an image first");
          return;
        }

        const formData = new FormData();
        formData.append("image", file);

        analyzeBtn.disabled = true;
        loading.style.display = "block";
        result.style.display = "none";

        try {
          const response = await fetch("/api/skin-analysis", {
            method: "POST",
            body: formData,
          });

          const data = await response.json();

          if (response.ok) {
            displaySkinAnalysisResults(data);
          } else {
            // Show user-friendly error message
            let errorMessage = data.error || "Unknown error occurred";
            if (data.details) {
              errorMessage += `\n\nDetails: ${data.details}`;
            }

            // Add helpful tips for common errors
            if (data.error && data.error.includes("clear, well-lit face")) {
              errorMessage +=
                "\n\nTips:\n• Use a high-quality image with good lighting\n• Ensure the face is clearly visible and not obscured\n• Face should be looking mostly forward (not too much side angle)\n• Image should be at least 400x400 pixels";
            }

            result.textContent = errorMessage;
            result.style.backgroundColor = "#f8d7da";
          }

          result.style.display = "block";
        } catch (error) {
          result.textContent = `Network Error: ${error.message}`;
          result.style.backgroundColor = "#f8d7da";
          result.style.display = "block";
        } finally {
          loading.style.display = "none";
          analyzeBtn.disabled = false;
          analyzeBtn.textContent = "Analyze Skin";
        }
      }

      function getScoreClass(score) {
        if (score >= 90) return "excellent";
        if (score >= 80) return "good";
        if (score >= 60) return "fair";
        if (score >= 40) return "poor";
        return "very-poor";
      }

      function displaySkinAnalysisResults(data) {
        const result = document.getElementById("result");
        const welcomeMessage = document.getElementById("welcomeMessage");
        const skinAge = data.result.skin_age?.value || "28";
        const scores = data.result.score_info;

        welcomeMessage.style.display = "none";

        const html = `
                <div class="skin-analysis-results">
                    <div class="skin-age-header">SKIN AGE: ${skinAge}</div>

                    <div class="scores-grid">
                        <div class="score-item">
                            <div class="score-circle skin-type">
                                <div class="score-value">...</div>
                            </div>
                            <div class="score-label">Skin Type</div>
                        </div>

                        <div class="score-item">
                            <div class="score-circle spots">
                                <div class="score-value">${scores.melanin_score}</div>
                            </div>
                            <div class="score-label">Spots</div>
                        </div>

                        <div class="score-item">
                            <div class="score-circle wrinkles">
                                <div class="score-value">${scores.wrinkle_score}</div>
                            </div>
                            <div class="score-label">Wrinkles</div>
                        </div>

                        <div class="score-item">
                            <div class="score-circle texture">
                                <div class="score-value">${scores.rough_score}</div>
                            </div>
                            <div class="score-label">Texture</div>
                        </div>

                        <div class="score-item">
                            <div class="score-circle acne">
                                <div class="score-value">${scores.acne_score}</div>
                            </div>
                            <div class="score-label">Acne</div>
                        </div>

                        <div class="score-item">
                            <div class="score-circle dark-circles">
                                <div class="score-value">${scores.dark_circle_score}</div>
                            </div>
                            <div class="score-label">Dark Circles</div>
                        </div>

                        <div class="score-item">
                            <div class="score-circle redness">
                                <div class="score-value">${scores.sensitivity_score}</div>
                            </div>
                            <div class="score-label">Redness</div>
                        </div>

                        <div class="score-item">
                            <div class="score-circle oiliness">
                                <div class="score-value">${scores.oily_intensity_score}</div>
                            </div>
                            <div class="score-label">Oiliness</div>
                        </div>

                        <div class="score-item">
                            <div class="score-circle moisture">
                                <div class="score-value">${scores.water_score}</div>
                            </div>
                            <div class="score-label">Moisture</div>
                        </div>

                        <div class="score-item">
                            <div class="score-circle pores">
                                <div class="score-value">${scores.pores_score}</div>
                            </div>
                            <div class="score-label">Pores</div>
                        </div>

                        <div class="score-item">
                            <div class="score-circle eye-bags">
                                <div class="score-value">...</div>
                            </div>
                            <div class="score-label">Eye bags</div>
                        </div>

                        <div class="score-item">
                            <div class="score-circle radiance">
                                <div class="score-value">...</div>
                            </div>
                            <div class="score-label">Radiance</div>
                        </div>

                        <div class="score-item">
                            <div class="score-circle firmness">
                                <div class="score-value">...</div>
                            </div>
                            <div class="score-label">Firmness</div>
                        </div>

                        <div class="score-item">
                            <div class="score-circle droopy-upper">
                                <div class="score-value">...</div>
                            </div>
                            <div class="score-label">Droopy Upper Eyelid</div>
                        </div>

                        <div class="score-item">
                            <div class="score-circle droopy-lower">
                                <div class="score-value">...</div>
                            </div>
                            <div class="score-label">Droopy Lower Eyelid</div>
                        </div>

                    </div>

                </div>
            `;

        result.innerHTML = html;
        result.style.backgroundColor = "transparent";
        result.style.display = "block";
      }

      function getSkinTypeText(type) {
        const types = ["Normal", "Dry", "Oily", "Combination"];
        return types[type] || "Unknown";
      }

      function getDarkCircleType(value) {
        const types = ["None", "Pigmented", "Vascular", "Structural"];
        return types[value] || "Unknown";
      }

      // Drag and drop functionality
      const uploadArea = document.querySelector(".upload-area");

      uploadArea.addEventListener("dragover", (e) => {
        e.preventDefault();
        uploadArea.style.borderColor = "#007bff";
        uploadArea.style.backgroundColor = "#f0f8ff";
      });

      uploadArea.addEventListener("dragleave", (e) => {
        e.preventDefault();
        uploadArea.style.borderColor = "#ccc";
        uploadArea.style.backgroundColor = "#fafafa";
      });

      uploadArea.addEventListener("drop", (e) => {
        e.preventDefault();
        uploadArea.style.borderColor = "#ccc";
        uploadArea.style.backgroundColor = "#fafafa";

        const files = e.dataTransfer.files;
        if (files.length > 0 && files[0].type.startsWith("image/")) {
          imageInput.files = files;
          analyzeBtn.disabled = false;
          analyzeBtn.textContent = `Analyze: ${files[0].name}`;

          // Show image preview
          const reader = new FileReader();
          reader.onload = function(e) {
            previewImg.src = e.target.result;
            imagePreview.style.display = "block";
            captureCircle.style.display = "none";
          };
          reader.readAsDataURL(files[0]);
        }
      });
    </script>
  </body>
</html>
