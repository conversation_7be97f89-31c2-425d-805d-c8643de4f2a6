<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Skin Analysis Test</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
      }
      .upload-area {
        border: 2px dashed #ccc;
        border-radius: 10px;
        padding: 40px;
        text-align: center;
        margin: 20px 0;
      }
      .upload-area:hover {
        border-color: #007bff;
      }
      .result {
        margin-top: 20px;
        padding: 20px;
        background-color: #f8f9fa;
        border-radius: 5px;
        white-space: pre-wrap;
      }
      .skin-analysis-results {
        background: white;
        border-radius: 15px;
        /* padding: 30px; */
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        margin-top: 20px;
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
      }
      .skin-age-header {
        text-align: center;
        font-size: 24px;
        font-weight: bold;
        color: #333;
        margin-bottom: 30px;
        padding-bottom: 15px;
        border-bottom: 2px solid #eee;
      }
      .scores-grid {
        display: grid;
        grid-template-columns: repeat(5, 1fr);
        gap: 20px;
        margin-bottom: 20px;
      }
      .score-item {
        text-align: center;
        padding: 15px;
      }

      .score-circle {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 16px;
        font-weight: bold;
        color: #fff;
        text-align: center;
        line-height: 1.2;
        flex-direction: column;
      }
      .score-label {
        font-size: 14px;
        color: #666;
        font-weight: 500;
      }
      .excellent {
        background-color: #28a745;
        border-color: #28a745;
      }
      .good {
        background-color: #17a2b8;
        border-color: #17a2b8;
      }
      .fair {
        background-color: #ffc107;
        border-color: #ffc107;
        color: #333 !important;
      }
      .poor {
        background-color: #fd7e14;
        border-color: #fd7e14;
      }
      .very-poor {
        background-color: #dc3545;
        border-color: #dc3545;
      }
      .details-section {
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid #eee;
      }
      .detail-item {
        display: flex;
        justify-content: space-between;
        padding: 8px 0;
        border-bottom: 1px solid #f0f0f0;
      }
      .detail-label {
        font-weight: 500;
        color: #555;
      }
      .detail-value {
        color: #333;
      }
      .loading {
        display: none;
        text-align: center;
        margin: 20px 0;
      }
      button {
        background-color: #007bff;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
      }
      button:hover {
        background-color: #0056b3;
      }
      button:disabled {
        background-color: #6c757d;
        cursor: not-allowed;
      }
    </style>
  </head>
  <body>
    <h1>Skin Analysis Test</h1>

    <div class="upload-area">
      <input
        type="file"
        id="imageInput"
        accept="image/*"
        style="display: none"
      />
      <p>Click to select an image or drag and drop</p>
      <button onclick="document.getElementById('imageInput').click()">
        Select Image
      </button>
    </div>

    <div>
      <button id="analyzeBtn" onclick="analyzeSkin()" disabled>
        Analyze Skin
      </button>
    </div>

    <div class="loading" id="loading">
      <p>Analyzing image... Please wait.</p>
    </div>

    <div class="result" id="result" style="display: none"></div>

    <script>
      const imageInput = document.getElementById("imageInput");
      const analyzeBtn = document.getElementById("analyzeBtn");
      const loading = document.getElementById("loading");
      const result = document.getElementById("result");

      imageInput.addEventListener("change", function (e) {
        if (e.target.files.length > 0) {
          analyzeBtn.disabled = false;
          analyzeBtn.textContent = `Analyze: ${e.target.files[0].name}`;
        }
      });

      async function analyzeSkin() {
        const file = imageInput.files[0];
        if (!file) {
          alert("Please select an image first");
          return;
        }

        const formData = new FormData();
        formData.append("image", file);

        analyzeBtn.disabled = true;
        loading.style.display = "block";
        result.style.display = "none";

        try {
          const response = await fetch("/api/skin-analysis", {
            method: "POST",
            body: formData,
          });

          const data = await response.json();

          if (response.ok) {
            displaySkinAnalysisResults(data);
          } else {
            // Show user-friendly error message
            let errorMessage = data.error || "Unknown error occurred";
            if (data.details) {
              errorMessage += `\n\nDetails: ${data.details}`;
            }

            // Add helpful tips for common errors
            if (data.error && data.error.includes("clear, well-lit face")) {
              errorMessage +=
                "\n\nTips:\n• Use a high-quality image with good lighting\n• Ensure the face is clearly visible and not obscured\n• Face should be looking mostly forward (not too much side angle)\n• Image should be at least 400x400 pixels";
            }

            result.textContent = errorMessage;
            result.style.backgroundColor = "#f8d7da";
          }

          result.style.display = "block";
        } catch (error) {
          result.textContent = `Network Error: ${error.message}`;
          result.style.backgroundColor = "#f8d7da";
          result.style.display = "block";
        } finally {
          loading.style.display = "none";
          analyzeBtn.disabled = false;
          analyzeBtn.textContent = "Analyze Skin";
        }
      }

      function getScoreClass(score) {
        if (score >= 90) return "excellent";
        if (score >= 80) return "good";
        if (score >= 60) return "fair";
        if (score >= 40) return "poor";
        return "very-poor";
      }

      function displaySkinAnalysisResults(data) {
        const result = document.getElementById("result");
        const skinAge = data.result.skin_age?.value || "N/A";
        const scores = data.result.score_info;

        const html = `
                <div class="skin-analysis-results">
                    <div class="skin-age-header">SKIN AGE: ${skinAge}</div>

                    <div class="scores-grid">
                        <div class="score-item">
                            <div class="score-circle ${getScoreClass(
                              scores.melanin_score
                            )}">
                                ${scores.melanin_score}
                            </div>
                            <div class="score-label">Spots</div>
                        </div>

                        <div class="score-item">
                            <div class="score-circle ${getScoreClass(
                              scores.wrinkle_score
                            )}">
                                ${scores.wrinkle_score}
                            </div>
                            <div class="score-label">Wrinkles</div>
                        </div>

                        <div class="score-item">
                            <div class="score-circle ${getScoreClass(
                              scores.rough_score
                            )}">
                                ${scores.rough_score}
                            </div>
                            <div class="score-label">Texture</div>
                        </div>

                        <div class="score-item">
                            <div class="score-circle ${getScoreClass(
                              scores.acne_score
                            )}">
                                ${scores.acne_score}
                            </div>
                            <div class="score-label">Acne</div>
                        </div>

                        <div class="score-item">
                            <div class="score-circle ${getScoreClass(
                              scores.dark_circle_score
                            )}">
                                ${scores.dark_circle_score}
                            </div>
                            <div class="score-label">Dark Circles</div>
                        </div>

                        <div class="score-item">
                            <div class="score-circle ${getScoreClass(
                              scores.sensitivity_score
                            )}">
                                ${scores.sensitivity_score}
                            </div>
                            <div class="score-label">Redness</div>
                        </div>

                        <div class="score-item">
                            <div class="score-circle ${getScoreClass(
                              scores.oily_intensity_score
                            )}">
                                ${scores.oily_intensity_score}
                            </div>
                            <div class="score-label">Oiliness</div>
                        </div>

                        <div class="score-item">
                            <div class="score-circle ${getScoreClass(
                              scores.water_score
                            )}">
                                ${scores.water_score}
                            </div>
                            <div class="score-label">Moisture</div>
                        </div>

                        <div class="score-item">
                            <div class="score-circle ${getScoreClass(
                              scores.pores_score
                            )}">
                                ${scores.pores_score}
                            </div>
                            <div class="score-label">Pores</div>
                        </div>

                        <div class="score-item">
                            <div class="score-circle ${getScoreClass(
                              scores.total_score
                            )}">
                                ${scores.total_score}
                            </div>
                            <div class="score-label">Overall</div>


                        </div>
                        
                    </div>

                </div>
            `;

        result.innerHTML = html;
        result.style.backgroundColor = "transparent";
        result.style.display = "block";
      }

      function getSkinTypeText(type) {
        const types = ["Normal", "Dry", "Oily", "Combination"];
        return types[type] || "Unknown";
      }

      function getDarkCircleType(value) {
        const types = ["None", "Pigmented", "Vascular", "Structural"];
        return types[value] || "Unknown";
      }

      // Drag and drop functionality
      const uploadArea = document.querySelector(".upload-area");

      uploadArea.addEventListener("dragover", (e) => {
        e.preventDefault();
        uploadArea.style.borderColor = "#007bff";
      });

      uploadArea.addEventListener("dragleave", (e) => {
        e.preventDefault();
        uploadArea.style.borderColor = "#ccc";
      });

      uploadArea.addEventListener("drop", (e) => {
        e.preventDefault();
        uploadArea.style.borderColor = "#ccc";

        const files = e.dataTransfer.files;
        if (files.length > 0 && files[0].type.startsWith("image/")) {
          imageInput.files = files;
          analyzeBtn.disabled = false;
          analyzeBtn.textContent = `Analyze: ${files[0].name}`;
        }
      });
    </script>
  </body>
</html>
