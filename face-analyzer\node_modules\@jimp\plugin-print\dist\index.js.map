{"version": 3, "sources": ["../src/index.js"], "names": ["xOffsetBasedOnAlignment", "constants", "font", "line", "max<PERSON><PERSON><PERSON>", "alignment", "HORIZONTAL_ALIGN_LEFT", "HORIZONTAL_ALIGN_CENTER", "<PERSON><PERSON><PERSON><PERSON>", "image", "x", "y", "char", "width", "height", "characterPage", "pages", "page", "blit", "xoffset", "yoffset", "printText", "text", "defaultCharWidth", "i", "length", "chars", "test", "fontChar", "fontKerning", "kernings", "kerning", "xadvance", "splitLines", "words", "split", "lines", "currentLine", "longestLine", "for<PERSON>ach", "word", "join", "push", "loadPages", "<PERSON><PERSON>", "dir", "newPages", "map", "read", "Promise", "all", "process", "env", "DIRNAME", "__dirname", "measureText", "measureTextHeight", "FONT_SANS_8_BLACK", "Path", "FONT_SANS_10_BLACK", "FONT_SANS_12_BLACK", "FONT_SANS_14_BLACK", "FONT_SANS_16_BLACK", "FONT_SANS_32_BLACK", "FONT_SANS_64_BLACK", "FONT_SANS_128_BLACK", "FONT_SANS_8_WHITE", "FONT_SANS_16_WHITE", "FONT_SANS_32_WHITE", "FONT_SANS_64_WHITE", "FONT_SANS_128_WHITE", "loadFont", "file", "cb", "throwError", "call", "resolve", "reject", "err", "String", "fromCharCode", "id", "firstString", "first", "second", "amount", "dirname", "then", "common", "info", "print", "maxHeight", "Infinity", "alignmentX", "alignmentY", "undefined", "constructor", "VERTICAL_ALIGN_TOP", "toString", "VERTICAL_ALIGN_BOTTOM", "VERTICAL_ALIGN_MIDDLE", "Object", "entries", "lineString", "alignmentWidth", "lineHeight"], "mappings": ";;;;;;;;;;;;;AAAA;;AACA;;AACA;;AACA;;AAEA,SAASA,uBAAT,CAAiCC,SAAjC,EAA4CC,IAA5C,EAAkDC,IAAlD,EAAwDC,QAAxD,EAAkEC,SAAlE,EAA6E;AAC3E,MAAIA,SAAS,KAAKJ,SAAS,CAACK,qBAA5B,EAAmD;AACjD,WAAO,CAAP;AACD;;AAED,MAAID,SAAS,KAAKJ,SAAS,CAACM,uBAA5B,EAAqD;AACnD,WAAO,CAACH,QAAQ,GAAG,8BAAYF,IAAZ,EAAkBC,IAAlB,CAAZ,IAAuC,CAA9C;AACD;;AAED,SAAOC,QAAQ,GAAG,8BAAYF,IAAZ,EAAkBC,IAAlB,CAAlB;AACD;;AAED,SAASK,aAAT,CAAuBC,KAAvB,EAA8BP,IAA9B,EAAoCQ,CAApC,EAAuCC,CAAvC,EAA0CC,KAA1C,EAAgD;AAC9C,MAAIA,KAAI,CAACC,KAAL,GAAa,CAAb,IAAkBD,KAAI,CAACE,MAAL,GAAc,CAApC,EAAuC;AACrC,QAAMC,aAAa,GAAGb,IAAI,CAACc,KAAL,CAAWJ,KAAI,CAACK,IAAhB,CAAtB;AAEAR,IAAAA,KAAK,CAACS,IAAN,CACEH,aADF,EAEEL,CAAC,GAAGE,KAAI,CAACO,OAFX,EAGER,CAAC,GAAGC,KAAI,CAACQ,OAHX,EAIER,KAAI,CAACF,CAJP,EAKEE,KAAI,CAACD,CALP,EAMEC,KAAI,CAACC,KANP,EAOED,KAAI,CAACE,MAPP;AASD;;AAED,SAAOL,KAAP;AACD;;AAED,SAASY,SAAT,CAAmBnB,IAAnB,EAAyBQ,CAAzB,EAA4BC,CAA5B,EAA+BW,IAA/B,EAAqCC,gBAArC,EAAuD;AACrD,OAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,IAAI,CAACG,MAAzB,EAAiCD,CAAC,EAAlC,EAAsC;AACpC,QAAIZ,MAAI,SAAR;;AAEA,QAAIV,IAAI,CAACwB,KAAL,CAAWJ,IAAI,CAACE,CAAD,CAAf,CAAJ,EAAyB;AACvBZ,MAAAA,MAAI,GAAGU,IAAI,CAACE,CAAD,CAAX;AACD,KAFD,MAEO,IAAI,KAAKG,IAAL,CAAUL,IAAI,CAACE,CAAD,CAAd,CAAJ,EAAwB;AAC7BZ,MAAAA,MAAI,GAAG,EAAP;AACD,KAFM,MAEA;AACLA,MAAAA,MAAI,GAAG,GAAP;AACD;;AAED,QAAMgB,QAAQ,GAAG1B,IAAI,CAACwB,KAAL,CAAWd,MAAX,KAAoB,EAArC;AACA,QAAMiB,WAAW,GAAG3B,IAAI,CAAC4B,QAAL,CAAclB,MAAd,CAApB;AAEAJ,IAAAA,aAAa,CAAC,IAAD,EAAON,IAAP,EAAaQ,CAAb,EAAgBC,CAAhB,EAAmBiB,QAAQ,IAAI,EAA/B,CAAb;AAEA,QAAMG,OAAO,GACXF,WAAW,IAAIA,WAAW,CAACP,IAAI,CAACE,CAAC,GAAG,CAAL,CAAL,CAA1B,GAA0CK,WAAW,CAACP,IAAI,CAACE,CAAC,GAAG,CAAL,CAAL,CAArD,GAAqE,CADvE;AAGAd,IAAAA,CAAC,IAAIqB,OAAO,IAAIH,QAAQ,CAACI,QAAT,IAAqBT,gBAAzB,CAAZ;AACD;AACF;;AAED,SAASU,UAAT,CAAoB/B,IAApB,EAA0BoB,IAA1B,EAAgClB,QAAhC,EAA0C;AACxC,MAAM8B,KAAK,GAAGZ,IAAI,CAACa,KAAL,CAAW,GAAX,CAAd;AACA,MAAMC,KAAK,GAAG,EAAd;AACA,MAAIC,WAAW,GAAG,EAAlB;AACA,MAAIC,WAAW,GAAG,CAAlB;AAEAJ,EAAAA,KAAK,CAACK,OAAN,CAAc,UAACC,IAAD,EAAU;AACtB,QAAMrC,IAAI,GAAG,8CAAIkC,WAAJ,IAAiBG,IAAjB,GAAuBC,IAAvB,CAA4B,GAA5B,CAAb;AACA,QAAMhB,MAAM,GAAG,8BAAYvB,IAAZ,EAAkBC,IAAlB,CAAf;;AAEA,QAAIsB,MAAM,IAAIrB,QAAd,EAAwB;AACtB,UAAIqB,MAAM,GAAGa,WAAb,EAA0B;AACxBA,QAAAA,WAAW,GAAGb,MAAd;AACD;;AAEDY,MAAAA,WAAW,CAACK,IAAZ,CAAiBF,IAAjB;AACD,KAND,MAMO;AACLJ,MAAAA,KAAK,CAACM,IAAN,CAAWL,WAAX;AACAA,MAAAA,WAAW,GAAG,CAACG,IAAD,CAAd;AACD;AACF,GAdD;AAgBAJ,EAAAA,KAAK,CAACM,IAAN,CAAWL,WAAX;AAEA,SAAO;AACLD,IAAAA,KAAK,EAALA,KADK;AAELE,IAAAA,WAAW,EAAXA;AAFK,GAAP;AAID;;AAED,SAASK,SAAT,CAAmBC,IAAnB,EAAyBC,GAAzB,EAA8B7B,KAA9B,EAAqC;AACnC,MAAM8B,QAAQ,GAAG9B,KAAK,CAAC+B,GAAN,CAAU,UAAC9B,IAAD,EAAU;AACnC,WAAO2B,IAAI,CAACI,IAAL,CAAUH,GAAG,GAAG,GAAN,GAAY5B,IAAtB,CAAP;AACD,GAFgB,CAAjB;AAIA,SAAOgC,OAAO,CAACC,GAAR,CAAYJ,QAAZ,CAAP;AACD;;AAED,IAAMD,GAAG,GAAGM,OAAO,CAACC,GAAR,CAAYC,OAAZ,cAA0BC,SAA1B,SAAZ;;eAEe;AAAA,SAAO;AACpBrD,IAAAA,SAAS,EAAE;AACTsD,MAAAA,WAAW,EAAXA,wBADS;AAETC,MAAAA,iBAAiB,EAAjBA,8BAFS;AAGTC,MAAAA,iBAAiB,EAAEC,iBAAKjB,IAAL,CACjBI,GADiB,EAEjB,yDAFiB,CAHV;AAOTc,MAAAA,kBAAkB,EAAED,iBAAKjB,IAAL,CAClBI,GADkB,EAElB,2DAFkB,CAPX;AAWTe,MAAAA,kBAAkB,EAAEF,iBAAKjB,IAAL,CAClBI,GADkB,EAElB,2DAFkB,CAXX;AAeTgB,MAAAA,kBAAkB,EAAEH,iBAAKjB,IAAL,CAClBI,GADkB,EAElB,2DAFkB,CAfX;AAmBTiB,MAAAA,kBAAkB,EAAEJ,iBAAKjB,IAAL,CAClBI,GADkB,EAElB,2DAFkB,CAnBX;AAuBTkB,MAAAA,kBAAkB,EAAEL,iBAAKjB,IAAL,CAClBI,GADkB,EAElB,2DAFkB,CAvBX;AA2BTmB,MAAAA,kBAAkB,EAAEN,iBAAKjB,IAAL,CAClBI,GADkB,EAElB,2DAFkB,CA3BX;AA+BToB,MAAAA,mBAAmB,EAAEP,iBAAKjB,IAAL,CACnBI,GADmB,EAEnB,6DAFmB,CA/BZ;AAoCTqB,MAAAA,iBAAiB,EAAER,iBAAKjB,IAAL,CACjBI,GADiB,EAEjB,yDAFiB,CApCV;AAwCTsB,MAAAA,kBAAkB,EAAET,iBAAKjB,IAAL,CAClBI,GADkB,EAElB,2DAFkB,CAxCX;AA4CTuB,MAAAA,kBAAkB,EAAEV,iBAAKjB,IAAL,CAClBI,GADkB,EAElB,2DAFkB,CA5CX;AAgDTwB,MAAAA,kBAAkB,EAAEX,iBAAKjB,IAAL,CAClBI,GADkB,EAElB,2DAFkB,CAhDX;AAoDTyB,MAAAA,mBAAmB,EAAEZ,iBAAKjB,IAAL,CACnBI,GADmB,EAEnB,6DAFmB,CApDZ;;AAyDT;;;;;;AAMA0B,MAAAA,QA/DS,oBA+DAC,IA/DA,EA+DMC,EA/DN,EA+DU;AAAA;;AACjB,YAAI,OAAOD,IAAP,KAAgB,QAApB,EACE,OAAOE,kBAAWC,IAAX,CAAgB,IAAhB,EAAsB,uBAAtB,EAA+CF,EAA/C,CAAP;AAEF,eAAO,IAAIxB,OAAJ,CAAY,UAAC2B,OAAD,EAAUC,MAAV,EAAqB;AACtCJ,UAAAA,EAAE,GACAA,EAAE,IACF,UAAUK,GAAV,EAAe5E,IAAf,EAAqB;AACnB,gBAAI4E,GAAJ,EAASD,MAAM,CAACC,GAAD,CAAN,CAAT,KACKF,OAAO,CAAC1E,IAAD,CAAP;AACN,WALH;;AAOA,sCAAOsE,IAAP,EAAa,UAACM,GAAD,EAAM5E,IAAN,EAAe;AAC1B,gBAAMwB,KAAK,GAAG,EAAd;AACA,gBAAMI,QAAQ,GAAG,EAAjB;;AAEA,gBAAIgD,GAAJ,EAAS;AACP,qBAAOJ,kBAAWC,IAAX,CAAgB,KAAhB,EAAsBG,GAAtB,EAA2BL,EAA3B,CAAP;AACD;;AAED,iBAAK,IAAIjD,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGtB,IAAI,CAACwB,KAAL,CAAWD,MAA/B,EAAuCD,CAAC,EAAxC,EAA4C;AAC1CE,cAAAA,KAAK,CAACqD,MAAM,CAACC,YAAP,CAAoB9E,IAAI,CAACwB,KAAL,CAAWF,CAAX,EAAcyD,EAAlC,CAAD,CAAL,GAA+C/E,IAAI,CAACwB,KAAL,CAAWF,CAAX,CAA/C;AACD;;AAED,iBAAK,IAAIA,EAAC,GAAG,CAAb,EAAgBA,EAAC,GAAGtB,IAAI,CAAC4B,QAAL,CAAcL,MAAlC,EAA0CD,EAAC,EAA3C,EAA+C;AAC7C,kBAAM0D,WAAW,GAAGH,MAAM,CAACC,YAAP,CAAoB9E,IAAI,CAAC4B,QAAL,CAAcN,EAAd,EAAiB2D,KAArC,CAApB;AACArD,cAAAA,QAAQ,CAACoD,WAAD,CAAR,GAAwBpD,QAAQ,CAACoD,WAAD,CAAR,IAAyB,EAAjD;AACApD,cAAAA,QAAQ,CAACoD,WAAD,CAAR,CACEH,MAAM,CAACC,YAAP,CAAoB9E,IAAI,CAAC4B,QAAL,CAAcN,EAAd,EAAiB4D,MAArC,CADF,IAEIlF,IAAI,CAAC4B,QAAL,CAAcN,EAAd,EAAiB6D,MAFrB;AAGD;;AAED1C,YAAAA,SAAS,CAAC,KAAD,EAAOe,iBAAK4B,OAAL,CAAad,IAAb,CAAP,EAA2BtE,IAAI,CAACc,KAAhC,CAAT,CAAgDuE,IAAhD,CAAqD,UAACvE,KAAD,EAAW;AAC9DyD,cAAAA,EAAE,CAAC,IAAD,EAAO;AACP/C,gBAAAA,KAAK,EAALA,KADO;AAEPI,gBAAAA,QAAQ,EAARA,QAFO;AAGPd,gBAAAA,KAAK,EAALA,KAHO;AAIPwE,gBAAAA,MAAM,EAAEtF,IAAI,CAACsF,MAJN;AAKPC,gBAAAA,IAAI,EAAEvF,IAAI,CAACuF;AALJ,eAAP,CAAF;AAOD,aARD;AASD,WA7BD;AA8BD,SAtCM,CAAP;AAuCD;AA1GQ,KADS;AA8GpB,aAAO;AACL;;;;;;;;;;;AAWAC,MAAAA,KAZK,iBAYCxF,IAZD,EAYOQ,CAZP,EAYUC,CAZV,EAYaW,IAZb,EAYmBlB,QAZnB,EAY6BuF,SAZ7B,EAYwClB,EAZxC,EAY4C;AAAA;;AAC/C,YAAI,OAAOrE,QAAP,KAAoB,UAApB,IAAkC,OAAOqE,EAAP,KAAc,WAApD,EAAiE;AAC/DA,UAAAA,EAAE,GAAGrE,QAAL;AACAA,UAAAA,QAAQ,GAAGwF,QAAX;AACD;;AAED,YAAI,OAAOxF,QAAP,KAAoB,WAAxB,EAAqC;AACnCA,UAAAA,QAAQ,GAAGwF,QAAX;AACD;;AAED,YAAI,OAAOD,SAAP,KAAqB,UAArB,IAAmC,OAAOlB,EAAP,KAAc,WAArD,EAAkE;AAChEA,UAAAA,EAAE,GAAGkB,SAAL;AACAA,UAAAA,SAAS,GAAGC,QAAZ;AACD;;AAED,YAAI,OAAOD,SAAP,KAAqB,WAAzB,EAAsC;AACpCA,UAAAA,SAAS,GAAGC,QAAZ;AACD;;AAED,YAAI,yBAAO1F,IAAP,MAAgB,QAApB,EAA8B;AAC5B,iBAAOwE,kBAAWC,IAAX,CAAgB,IAAhB,EAAsB,8BAAtB,EAAsDF,EAAtD,CAAP;AACD;;AAED,YACE,OAAO/D,CAAP,KAAa,QAAb,IACA,OAAOC,CAAP,KAAa,QADb,IAEA,OAAOP,QAAP,KAAoB,QAHtB,EAIE;AACA,iBAAOsE,kBAAWC,IAAX,CAAgB,IAAhB,EAAsB,mCAAtB,EAA2DF,EAA3D,CAAP;AACD;;AAED,YAAI,OAAOrE,QAAP,KAAoB,QAAxB,EAAkC;AAChC,iBAAOsE,kBAAWC,IAAX,CAAgB,IAAhB,EAAsB,2BAAtB,EAAmDF,EAAnD,CAAP;AACD;;AAED,YAAI,OAAOkB,SAAP,KAAqB,QAAzB,EAAmC;AACjC,iBAAOjB,kBAAWC,IAAX,CAAgB,IAAhB,EAAsB,4BAAtB,EAAoDF,EAApD,CAAP;AACD;;AAED,YAAIoB,UAAJ;AACA,YAAIC,UAAJ;;AAEA,YACE,yBAAOxE,IAAP,MAAgB,QAAhB,IACAA,IAAI,CAACA,IAAL,KAAc,IADd,IAEAA,IAAI,CAACA,IAAL,KAAcyE,SAHhB,EAIE;AACAF,UAAAA,UAAU,GAAGvE,IAAI,CAACuE,UAAL,IAAmB,KAAKG,WAAL,CAAiB1F,qBAAjD;AACAwF,UAAAA,UAAU,GAAGxE,IAAI,CAACwE,UAAL,IAAmB,KAAKE,WAAL,CAAiBC,kBAAjD;AAFA,sBAGY3E,IAHZ;AAGGA,UAAAA,IAHH,SAGGA,IAHH;AAID,SARD,MAQO;AACLuE,UAAAA,UAAU,GAAG,KAAKG,WAAL,CAAiB1F,qBAA9B;AACAwF,UAAAA,UAAU,GAAG,KAAKE,WAAL,CAAiBC,kBAA9B;AACA3E,UAAAA,IAAI,GAAGA,IAAI,CAAC4E,QAAL,EAAP;AACD;;AAED,YACEP,SAAS,KAAKC,QAAd,IACAE,UAAU,KAAK,KAAKE,WAAL,CAAiBG,qBAFlC,EAGE;AACAxF,UAAAA,CAAC,IAAIgF,SAAS,GAAG,oCAAkBzF,IAAlB,EAAwBoB,IAAxB,EAA8BlB,QAA9B,CAAjB;AACD,SALD,MAKO,IACLuF,SAAS,KAAKC,QAAd,IACAE,UAAU,KAAK,KAAKE,WAAL,CAAiBI,qBAF3B,EAGL;AACAzF,UAAAA,CAAC,IAAIgF,SAAS,GAAG,CAAZ,GAAgB,oCAAkBzF,IAAlB,EAAwBoB,IAAxB,EAA8BlB,QAA9B,IAA0C,CAA/D;AACD;;AAED,YAAMmB,gBAAgB,GAAG8E,MAAM,CAACC,OAAP,CAAepG,IAAI,CAACwB,KAApB,EAA2B,CAA3B,EAA8B,CAA9B,EAAiCM,QAA1D;;AApE+C,0BAqEhBC,UAAU,CAAC/B,IAAD,EAAOoB,IAAP,EAAalB,QAAb,CArEM;AAAA,YAqEvCgC,KArEuC,eAqEvCA,KArEuC;AAAA,YAqEhCE,WArEgC,eAqEhCA,WArEgC;;AAuE/CF,QAAAA,KAAK,CAACG,OAAN,CAAc,UAACpC,IAAD,EAAU;AACtB,cAAMoG,UAAU,GAAGpG,IAAI,CAACsC,IAAL,CAAU,GAAV,CAAnB;AACA,cAAM+D,cAAc,GAAGxG,uBAAuB,CAC5C,MAAI,CAACgG,WADuC,EAE5C9F,IAF4C,EAG5CqG,UAH4C,EAI5CnG,QAJ4C,EAK5CyF,UAL4C,CAA9C;AAQAxE,UAAAA,SAAS,CAACsD,IAAV,CACE,MADF,EAEEzE,IAFF,EAGEQ,CAAC,GAAG8F,cAHN,EAIE7F,CAJF,EAKE4F,UALF,EAMEhF,gBANF;AASAZ,UAAAA,CAAC,IAAIT,IAAI,CAACsF,MAAL,CAAYiB,UAAjB;AACD,SApBD;;AAsBA,YAAI,0BAAchC,EAAd,CAAJ,EAAuB;AACrBA,UAAAA,EAAE,CAACE,IAAH,CAAQ,IAAR,EAAc,IAAd,EAAoB,IAApB,EAA0B;AAAEjE,YAAAA,CAAC,EAAEA,CAAC,GAAG4B,WAAT;AAAsB3B,YAAAA,CAAC,EAADA;AAAtB,WAA1B;AACD;;AAED,eAAO,IAAP;AACD;AA9GI;AA9Ga,GAAP;AAAA,C", "sourcesContent": ["import Path from \"path\";\nimport bMFont from \"load-bmfont\";\nimport { isNodePattern, throwError } from \"@jimp/utils\";\nimport { measureText, measureTextHeight } from \"./measure-text\";\n\nfunction xOffsetBasedOnAlignment(constants, font, line, maxWidth, alignment) {\n  if (alignment === constants.HORIZONTAL_ALIGN_LEFT) {\n    return 0;\n  }\n\n  if (alignment === constants.HORIZONTAL_ALIGN_CENTER) {\n    return (maxWidth - measureText(font, line)) / 2;\n  }\n\n  return maxWidth - measureText(font, line);\n}\n\nfunction drawCharacter(image, font, x, y, char) {\n  if (char.width > 0 && char.height > 0) {\n    const characterPage = font.pages[char.page];\n\n    image.blit(\n      characterPage,\n      x + char.xoffset,\n      y + char.yoffset,\n      char.x,\n      char.y,\n      char.width,\n      char.height\n    );\n  }\n\n  return image;\n}\n\nfunction printText(font, x, y, text, defaultCharWidth) {\n  for (let i = 0; i < text.length; i++) {\n    let char;\n\n    if (font.chars[text[i]]) {\n      char = text[i];\n    } else if (/\\s/.test(text[i])) {\n      char = \"\";\n    } else {\n      char = \"?\";\n    }\n\n    const fontChar = font.chars[char] || {};\n    const fontKerning = font.kernings[char];\n\n    drawCharacter(this, font, x, y, fontChar || {});\n\n    const kerning =\n      fontKerning && fontKerning[text[i + 1]] ? fontKerning[text[i + 1]] : 0;\n\n    x += kerning + (fontChar.xadvance || defaultCharWidth);\n  }\n}\n\nfunction splitLines(font, text, maxWidth) {\n  const words = text.split(\" \");\n  const lines = [];\n  let currentLine = [];\n  let longestLine = 0;\n\n  words.forEach((word) => {\n    const line = [...currentLine, word].join(\" \");\n    const length = measureText(font, line);\n\n    if (length <= maxWidth) {\n      if (length > longestLine) {\n        longestLine = length;\n      }\n\n      currentLine.push(word);\n    } else {\n      lines.push(currentLine);\n      currentLine = [word];\n    }\n  });\n\n  lines.push(currentLine);\n\n  return {\n    lines,\n    longestLine,\n  };\n}\n\nfunction loadPages(Jimp, dir, pages) {\n  const newPages = pages.map((page) => {\n    return Jimp.read(dir + \"/\" + page);\n  });\n\n  return Promise.all(newPages);\n}\n\nconst dir = process.env.DIRNAME || `${__dirname}/../`;\n\nexport default () => ({\n  constants: {\n    measureText,\n    measureTextHeight,\n    FONT_SANS_8_BLACK: Path.join(\n      dir,\n      \"fonts/open-sans/open-sans-8-black/open-sans-8-black.fnt\"\n    ),\n    FONT_SANS_10_BLACK: Path.join(\n      dir,\n      \"fonts/open-sans/open-sans-10-black/open-sans-10-black.fnt\"\n    ),\n    FONT_SANS_12_BLACK: Path.join(\n      dir,\n      \"fonts/open-sans/open-sans-12-black/open-sans-12-black.fnt\"\n    ),\n    FONT_SANS_14_BLACK: Path.join(\n      dir,\n      \"fonts/open-sans/open-sans-14-black/open-sans-14-black.fnt\"\n    ),\n    FONT_SANS_16_BLACK: Path.join(\n      dir,\n      \"fonts/open-sans/open-sans-16-black/open-sans-16-black.fnt\"\n    ),\n    FONT_SANS_32_BLACK: Path.join(\n      dir,\n      \"fonts/open-sans/open-sans-32-black/open-sans-32-black.fnt\"\n    ),\n    FONT_SANS_64_BLACK: Path.join(\n      dir,\n      \"fonts/open-sans/open-sans-64-black/open-sans-64-black.fnt\"\n    ),\n    FONT_SANS_128_BLACK: Path.join(\n      dir,\n      \"fonts/open-sans/open-sans-128-black/open-sans-128-black.fnt\"\n    ),\n\n    FONT_SANS_8_WHITE: Path.join(\n      dir,\n      \"fonts/open-sans/open-sans-8-white/open-sans-8-white.fnt\"\n    ),\n    FONT_SANS_16_WHITE: Path.join(\n      dir,\n      \"fonts/open-sans/open-sans-16-white/open-sans-16-white.fnt\"\n    ),\n    FONT_SANS_32_WHITE: Path.join(\n      dir,\n      \"fonts/open-sans/open-sans-32-white/open-sans-32-white.fnt\"\n    ),\n    FONT_SANS_64_WHITE: Path.join(\n      dir,\n      \"fonts/open-sans/open-sans-64-white/open-sans-64-white.fnt\"\n    ),\n    FONT_SANS_128_WHITE: Path.join(\n      dir,\n      \"fonts/open-sans/open-sans-128-white/open-sans-128-white.fnt\"\n    ),\n\n    /**\n     * Loads a bitmap font from a file\n     * @param {string} file the file path of a .fnt file\n     * @param {function(Error, Jimp)} cb (optional) a function to call when the font is loaded\n     * @returns {Promise} a promise\n     */\n    loadFont(file, cb) {\n      if (typeof file !== \"string\")\n        return throwError.call(this, \"file must be a string\", cb);\n\n      return new Promise((resolve, reject) => {\n        cb =\n          cb ||\n          function (err, font) {\n            if (err) reject(err);\n            else resolve(font);\n          };\n\n        bMFont(file, (err, font) => {\n          const chars = {};\n          const kernings = {};\n\n          if (err) {\n            return throwError.call(this, err, cb);\n          }\n\n          for (let i = 0; i < font.chars.length; i++) {\n            chars[String.fromCharCode(font.chars[i].id)] = font.chars[i];\n          }\n\n          for (let i = 0; i < font.kernings.length; i++) {\n            const firstString = String.fromCharCode(font.kernings[i].first);\n            kernings[firstString] = kernings[firstString] || {};\n            kernings[firstString][\n              String.fromCharCode(font.kernings[i].second)\n            ] = font.kernings[i].amount;\n          }\n\n          loadPages(this, Path.dirname(file), font.pages).then((pages) => {\n            cb(null, {\n              chars,\n              kernings,\n              pages,\n              common: font.common,\n              info: font.info,\n            });\n          });\n        });\n      });\n    },\n  },\n\n  class: {\n    /**\n     * Draws a text on a image on a given boundary\n     * @param {Jimp} font a bitmap font loaded from `Jimp.loadFont` command\n     * @param {number} x the x position to start drawing the text\n     * @param {number} y the y position to start drawing the text\n     * @param {any} text the text to draw (string or object with `text`, `alignmentX`, and/or `alignmentY`)\n     * @param {number} maxWidth (optional) the boundary width to draw in\n     * @param {number} maxHeight (optional) the boundary height to draw in\n     * @param {function(Error, Jimp)} cb (optional) a function to call when the text is written\n     * @returns {Jimp} this for chaining of methods\n     */\n    print(font, x, y, text, maxWidth, maxHeight, cb) {\n      if (typeof maxWidth === \"function\" && typeof cb === \"undefined\") {\n        cb = maxWidth;\n        maxWidth = Infinity;\n      }\n\n      if (typeof maxWidth === \"undefined\") {\n        maxWidth = Infinity;\n      }\n\n      if (typeof maxHeight === \"function\" && typeof cb === \"undefined\") {\n        cb = maxHeight;\n        maxHeight = Infinity;\n      }\n\n      if (typeof maxHeight === \"undefined\") {\n        maxHeight = Infinity;\n      }\n\n      if (typeof font !== \"object\") {\n        return throwError.call(this, \"font must be a Jimp loadFont\", cb);\n      }\n\n      if (\n        typeof x !== \"number\" ||\n        typeof y !== \"number\" ||\n        typeof maxWidth !== \"number\"\n      ) {\n        return throwError.call(this, \"x, y and maxWidth must be numbers\", cb);\n      }\n\n      if (typeof maxWidth !== \"number\") {\n        return throwError.call(this, \"maxWidth must be a number\", cb);\n      }\n\n      if (typeof maxHeight !== \"number\") {\n        return throwError.call(this, \"maxHeight must be a number\", cb);\n      }\n\n      let alignmentX;\n      let alignmentY;\n\n      if (\n        typeof text === \"object\" &&\n        text.text !== null &&\n        text.text !== undefined\n      ) {\n        alignmentX = text.alignmentX || this.constructor.HORIZONTAL_ALIGN_LEFT;\n        alignmentY = text.alignmentY || this.constructor.VERTICAL_ALIGN_TOP;\n        ({ text } = text);\n      } else {\n        alignmentX = this.constructor.HORIZONTAL_ALIGN_LEFT;\n        alignmentY = this.constructor.VERTICAL_ALIGN_TOP;\n        text = text.toString();\n      }\n\n      if (\n        maxHeight !== Infinity &&\n        alignmentY === this.constructor.VERTICAL_ALIGN_BOTTOM\n      ) {\n        y += maxHeight - measureTextHeight(font, text, maxWidth);\n      } else if (\n        maxHeight !== Infinity &&\n        alignmentY === this.constructor.VERTICAL_ALIGN_MIDDLE\n      ) {\n        y += maxHeight / 2 - measureTextHeight(font, text, maxWidth) / 2;\n      }\n\n      const defaultCharWidth = Object.entries(font.chars)[0][1].xadvance;\n      const { lines, longestLine } = splitLines(font, text, maxWidth);\n\n      lines.forEach((line) => {\n        const lineString = line.join(\" \");\n        const alignmentWidth = xOffsetBasedOnAlignment(\n          this.constructor,\n          font,\n          lineString,\n          maxWidth,\n          alignmentX\n        );\n\n        printText.call(\n          this,\n          font,\n          x + alignmentWidth,\n          y,\n          lineString,\n          defaultCharWidth\n        );\n\n        y += font.common.lineHeight;\n      });\n\n      if (isNodePattern(cb)) {\n        cb.call(this, null, this, { x: x + longestLine, y });\n      }\n\n      return this;\n    },\n  },\n});\n"], "file": "index.js"}