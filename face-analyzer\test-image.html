<!DOCTYPE html>
<html>
<head>
    <title>Test Image Generator</title>
</head>
<body>
    <canvas id="testCanvas" width="400" height="400" style="border: 1px solid #ccc;"></canvas>
    <br><br>
    <button onclick="downloadImage()">Download Test Image</button>
    
    <script>
        const canvas = document.getElementById('testCanvas');
        const ctx = canvas.getContext('2d');
        
        // Create a simple face-like test image
        ctx.fillStyle = '#F4C2A1'; // Skin tone
        ctx.fillRect(0, 0, 400, 400);
        
        // Face outline
        ctx.fillStyle = '#E8B896';
        ctx.beginPath();
        ctx.ellipse(200, 200, 150, 180, 0, 0, 2 * Math.PI);
        ctx.fill();
        
        // Eyes
        ctx.fillStyle = '#333';
        ctx.beginPath();
        ctx.ellipse(170, 170, 15, 20, 0, 0, 2 * Math.PI);
        ctx.fill();
        
        ctx.beginPath();
        ctx.ellipse(230, 170, 15, 20, 0, 0, 2 * Math.PI);
        ctx.fill();
        
        // Nose
        ctx.fillStyle = '#D4A574';
        ctx.beginPath();
        ctx.ellipse(200, 200, 8, 15, 0, 0, 2 * Math.PI);
        ctx.fill();
        
        // Mouth
        ctx.fillStyle = '#B8860B';
        ctx.beginPath();
        ctx.ellipse(200, 230, 20, 8, 0, 0, 2 * Math.PI);
        ctx.fill();
        
        // Add some texture/spots for testing
        ctx.fillStyle = '#D4A574';
        for(let i = 0; i < 20; i++) {
            const x = Math.random() * 300 + 50;
            const y = Math.random() * 360 + 20;
            ctx.beginPath();
            ctx.arc(x, y, Math.random() * 3 + 1, 0, 2 * Math.PI);
            ctx.fill();
        }
        
        function downloadImage() {
            const link = document.createElement('a');
            link.download = 'test-face.png';
            link.href = canvas.toDataURL();
            link.click();
        }
    </script>
</body>
</html>
